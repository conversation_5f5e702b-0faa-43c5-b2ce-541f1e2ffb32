// Dữ liệu ảo cho ứng dụng CRM

export interface BoxAddInfoItem {
    title?: string;
    infoAdd?: string;
    label: string;
    value: string;
    boxColor?: string;
    onViewQuick?: () => void;
    onViewDetail?: () => void;
    onDelete?: () => void;
}

// Dữ liệu ảo cho BoxAddInfo component
export const mockBoxAddInfoData: BoxAddInfoItem[] = [
    {
        title: 'Ghi chú quan trọng',
        infoAdd: 'Thêm mới 2 ngày trước',
        label: 'Loại',
        value: 'Ghi chú cuộc họp với khách hàng',
        boxColor: '#e3f2fd',
        onViewQuick: () => alert('Xem nhanh ghi chú 1'),
        onViewDetail: () => alert('Xem chi tiết ghi chú 1'),
        onDelete: () => alert('Xóa ghi chú 1'),
    },
    {
        title: 'Lịch hẹn sắp tới',
        infoAdd: 'Hôm nay 14:30',
        label: 'Địa điểm',
        value: 'Văn phòng Garena - Tầng 12',
        boxColor: '#fff3e0',
        onViewQuick: () => alert('Xem nhanh lịch hẹn 1'),
        onViewDetail: () => alert('Xem chi tiết lịch hẹn 1'),
        onDelete: () => alert('Xóa lịch hẹn 1'),
    },
    {
        title: 'Tài liệu tham khảo',
        infoAdd: 'Cập nhật 1 tuần trước',
        label: 'Định dạng',
        value: 'PDF - 2.5MB',
        boxColor: '#f3e5f5',
        onViewQuick: () => alert('Xem nhanh tài liệu 1'),
        onViewDetail: () => alert('Xem chi tiết tài liệu 1'),
        onDelete: () => alert('Xóa tài liệu 1'),
    },
    {
        title: 'Email quan trọng',
        infoAdd: 'Nhận được 3 ngày trước',
        label: 'Người gửi',
        value: '<EMAIL>',
        boxColor: '#e8f5e8',
        onViewQuick: () => alert('Xem nhanh email 1'),
        onViewDetail: () => alert('Xem chi tiết email 1'),
        onDelete: () => alert('Xóa email 1'),
    },
    {
        title: 'Báo cáo doanh số',
        infoAdd: 'Tạo hôm qua',
        label: 'Kỳ báo cáo',
        value: 'Quý 4/2024',
        boxColor: '#fff8e1',
        onViewQuick: () => alert('Xem nhanh báo cáo 1'),
        onViewDetail: () => alert('Xem chi tiết báo cáo 1'),
        onDelete: () => alert('Xóa báo cáo 1'),
    },
];

// Dữ liệu ảo cho khách hàng
export const mockCustomerData = [
    {
        id: 1,
        name: 'Garena',
        companyName: 'Công Ty CP Garena Việt Nam',
        email: '<EMAIL>',
        phone: '*********',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: 'Tiềm năng',
        status: 'Mới',
        address: '123 Nguyễn Văn Linh, Quận 7, TP.HCM',
        industry: 'Công nghệ thông tin',
        revenue: '50 tỷ VNĐ',
    },
    {
        id: 2,
        name: 'VNG Corporation',
        companyName: 'Công Ty CP VNG',
        email: '<EMAIL>',
        phone: '0283838383',
        employees: '1000 nhân viên',
        website: 'www.vng.com.vn',
        score: '250',
        stage: 'Đang đàm phán',
        status: 'Quan tâm',
        address: '182 Lê Đại Hành, Quận 11, TP.HCM',
        industry: 'Game & Entertainment',
        revenue: '100 tỷ VNĐ',
    },
    {
        id: 3,
        name: 'FPT Software',
        companyName: 'Công Ty TNHH Phần Mềm FPT',
        email: '<EMAIL>',
        phone: '0243577777',
        employees: '2000 nhân viên',
        website: 'www.fpt-software.com',
        score: '300',
        stage: 'Đã ký hợp đồng',
        status: 'Khách hàng',
        address: '17 Duy Tân, Cầu Giấy, Hà Nội',
        industry: 'Phần mềm',
        revenue: '200 tỷ VNĐ',
    },
];

// Dữ liệu ảo cho đối tác
export const mockPartnerData = [
    {
        id: 1,
        name: 'Microsoft Vietnam',
        companyName: 'Microsoft Vietnam Company Limited',
        email: '<EMAIL>',
        phone: '0243946000',
        employees: '300 nhân viên',
        website: 'www.microsoft.com/vi-vn',
        score: '400',
        stage: 'Đối tác chiến lược',
        status: 'Hoạt động',
        address: 'Tầng 8, Lotte Center, 54 Liễu Giai, Ba Đình, Hà Nội',
        industry: 'Công nghệ',
        partnershipType: 'Technology Partner',
    },
    {
        id: 2,
        name: 'Google Vietnam',
        companyName: 'Google Vietnam Company Limited',
        email: '<EMAIL>',
        phone: '0283925000',
        employees: '200 nhân viên',
        website: 'www.google.com.vn',
        score: '450',
        stage: 'Đối tác chính',
        status: 'Hoạt động',
        address: 'Tầng 4-6, Lim Tower, 9-11 Tôn Đức Thắng, Quận 1, TP.HCM',
        industry: 'Internet & Technology',
        partnershipType: 'Strategic Partner',
    },
];

// Dữ liệu ảo cho file đính kèm
export const mockFileData = [
    {
        id: 1,
        name: 'CRM_Proposal.docx',
        icon: 'ri-file-text-line',
        date: '11/01/2025',
        size: '2.5MB',
        type: 'document',
        uploadedBy: 'Nguyễn Văn A',
    },
    {
        id: 2,
        name: 'Presentation_Q4.ppt',
        icon: 'ri-file-ppt-line',
        date: '10/01/2025',
        size: '15.2MB',
        type: 'presentation',
        uploadedBy: 'Trần Thị B',
    },
    {
        id: 3,
        name: 'Contract_2025.pdf',
        icon: 'ri-file-pdf-line',
        date: '09/01/2025',
        size: '1.8MB',
        type: 'pdf',
        uploadedBy: 'Lê Văn C',
    },
    {
        id: 4,
        name: 'Budget_Analysis.xlsx',
        icon: 'ri-file-excel-line',
        date: '08/01/2025',
        size: '3.2MB',
        type: 'spreadsheet',
        uploadedBy: 'Phạm Thị D',
    },
];

// Dữ liệu ảo cho hoạt động
export const mockActivityData = [
    {
        id: 1,
        type: 'call',
        title: 'Cuộc gọi với khách hàng',
        description: 'Thảo luận về yêu cầu dự án mới',
        date: '11/01/2025 14:30',
        duration: '45 phút',
        status: 'Hoàn thành',
        assignee: 'Nguyễn Văn A',
    },
    {
        id: 2,
        type: 'meeting',
        title: 'Họp với đội ngũ',
        description: 'Review tiến độ dự án và lập kế hoạch tuần tới',
        date: '10/01/2025 09:00',
        duration: '2 giờ',
        status: 'Hoàn thành',
        assignee: 'Trần Thị B',
    },
    {
        id: 3,
        type: 'email',
        title: 'Gửi báo giá',
        description: 'Gửi báo giá chi tiết cho dự án CRM',
        date: '09/01/2025 16:20',
        duration: '30 phút',
        status: 'Đã gửi',
        assignee: 'Lê Văn C',
    },
    {
        id: 4,
        type: 'task',
        title: 'Chuẩn bị tài liệu',
        description: 'Chuẩn bị tài liệu demo cho cuộc họp ngày mai',
        date: '12/01/2025 10:00',
        duration: '1 giờ',
        status: 'Đang thực hiện',
        assignee: 'Phạm Thị D',
    },
];

// Hàm helper để tạo dữ liệu ảo
export const generateMockData = {
    // Tạo dữ liệu BoxAddInfo ngẫu nhiên
    createBoxAddInfoItems: (count: number): BoxAddInfoItem[] => {
        const titles = ['Ghi chú', 'Lịch hẹn', 'Tài liệu', 'Email', 'Báo cáo', 'Nhiệm vụ'];
        const labels = ['Loại', 'Địa điểm', 'Định dạng', 'Người gửi', 'Kỳ báo cáo', 'Ưu tiên'];
        const values = ['Quan trọng', 'Văn phòng', 'PDF', '<EMAIL>', 'Q4/2024', 'Cao'];
        const colors = ['#e3f2fd', '#fff3e0', '#f3e5f5', '#e8f5e8', '#fff8e1', '#fce4ec'];

        return Array.from({ length: count }, (_, index) => ({
            title: `${titles[index % titles.length]} ${index + 1}`,
            infoAdd: `Cập nhật ${Math.floor(Math.random() * 7) + 1} ngày trước`,
            label: labels[index % labels.length],
            value: values[index % values.length],
            boxColor: colors[index % colors.length],
            onViewQuick: () => alert(`Xem nhanh ${titles[index % titles.length]} ${index + 1}`),
            onViewDetail: () => alert(`Xem chi tiết ${titles[index % titles.length]} ${index + 1}`),
            onDelete: () => alert(`Xóa ${titles[index % titles.length]} ${index + 1}`),
        }));
    },
};
