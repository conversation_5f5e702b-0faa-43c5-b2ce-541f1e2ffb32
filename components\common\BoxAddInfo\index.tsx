import {
    <PERSON><PERSON>,
    <PERSON>,
    CardBody,
    Card<PERSON>eader,
    Col,
    Label,
    Row,
} from 'reactstrap';
import DropdownActionMenu from '../DropdownActionMenu';

interface BoxAddInfoItem {
    title?: string;
    infoAdd?: string;
    data: [
        {
            label: string;
            value: string;
            boxColor?: boolean;
        },
    ];
    onViewQuick?: () => void;
    onViewDetail?: () => void;
    onDelete?: () => void;
}

interface BoxAddInfoProps {
    title: string;
    length: number;
    onAdd?: () => void;
    content: BoxAddInfoItem[];
}

const BoxAddInfo = ({
    title,
    length,
    content = [],
    onAdd,
}: BoxAddInfoProps) => {
    return (
        <Col md={12}>
            <Card className='mb-3'>
                <CardHeader>
                    <Row>
                        <Col md={9}>
                            <h5 className='mb-0'>
                                {title} ({length})
                            </h5>
                        </Col>
                        <Col md={3}>
                            <Button
                                color='success'
                                size='sm'
                                onClick={onAdd}
                                style={{
                                    backgroundColor: '#ffffff',
                                    borderColor: '#0ab39c',
                                    color: '#0ab39c',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>
                                Thêm
                            </Button>
                        </Col>
                    </Row>
                </CardHeader>
                <CardBody>
                    {content.map((item, index) => (
                        <div key={index} className='mb-3'>
                            <div className='p-3 rounded'>
                                <div className='d-flex align-items-center justify-content-between mb-2'>
                                    <h6 className='mb-0'>{item.title}</h6>
                                    <DropdownActionMenu
                                        actions={[
                                            {
                                                icon: 'ri-eye-line',
                                                label: 'Xem nhanh',
                                                onClick:
                                                    item.onViewQuick ||
                                                    (() => {}),
                                            },
                                            {
                                                icon: 'ri-eye-fill',
                                                label: 'Xem chi tiết',
                                                onClick:
                                                    item.onViewDetail ||
                                                    (() => {}),
                                            },
                                            {
                                                icon: 'ri-delete-bin-line',
                                                label: 'Xóa',
                                                onClick:
                                                    item.onDelete || (() => {}),
                                                className: 'text-danger',
                                            },
                                        ]}
                                        toggleIcon='ri-more-2-fill'
                                    />
                                </div>
                                {item.infoAdd && (
                                    <small className='text-muted d-block mb-2'>
                                        {item.infoAdd}
                                    </small>
                                )}
                                {item.data.map((dataItem, dataIndex) => (
                                    <div
                                        key={dataIndex}
                                        className='d-flex gap-2 mb-1'
                                    >
                                        <Label>{dataItem.label}:</Label>
                                        {dataItem.boxColor ? (
                                            <div
                                                style={{
                                                    display: 'inline-flex',
                                                    alignItems: 'center',
                                                    backgroundColor: '#daf4f0',
                                                    border: '1px solid #b8e6dd',
                                                    borderRadius: '6px',
                                                    color: '#0ab39c',
                                                    padding: '4px 12px',
                                                    fontSize: '0.875rem',
                                                    fontWeight: '500',
                                                    lineHeight: '1.2',
                                                    boxShadow: '0 1px 2px rgba(10, 179, 156, 0.1)',
                                                    transition: 'all 0.2s ease-in-out',
                                                    cursor: 'default',
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.currentTarget.style.backgroundColor = '#c5f0e9';
                                                    e.currentTarget.style.borderColor = '#9dd9cc';
                                                    e.currentTarget.style.transform = 'translateY(-1px)';
                                                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(10, 179, 156, 0.15)';
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.currentTarget.style.backgroundColor = '#daf4f0';
                                                    e.currentTarget.style.borderColor = '#b8e6dd';
                                                    e.currentTarget.style.transform = 'translateY(0)';
                                                    e.currentTarget.style.boxShadow = '0 1px 2px rgba(10, 179, 156, 0.1)';
                                                }}
                                            >
                                                {dataItem.value}
                                            </div>
                                        ) : (
                                            <p className="mb-0">{dataItem.value}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}

                    {content.length === 0 && (
                        <div className='text-center text-muted py-4'>
                            <i className='ri-inbox-line fs-24 mb-2 d-block'></i>
                            Chưa có dữ liệu
                        </div>
                    )}
                </CardBody>
            </Card>
        </Col>
    );
};

export default BoxAddInfo;
